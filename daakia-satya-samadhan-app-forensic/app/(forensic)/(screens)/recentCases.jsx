import {
  View,
  Text,
  useWindowDimensions,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  StyleSheet,
} from "react-native";
import { useEffect, useState, useCallback, useMemo } from "react";
import { router, useLocalSearchParams } from "expo-router";
import { useAuth } from "../../../context/auth-context";
import { Colors } from "../../../constants/colors";
import { RECENT_CASES_STATUS, RECENT_CASES_STATUS_DISPLAY_NAMES } from "../../../services/validation";
import debounce from 'lodash/debounce';
import RecentCasesSkeleton from "../../../components/SkeletonLoaders/RecentCasesSkeleton";
import { apiService } from "../../../services/api";
import { validationService } from "../../../services/validation";
import { timeUtils } from "../../../utils/currentTime";
import StatusFilter from "../../../components/Smalls/StatusFilter";
import SearchBar from "../../../components/Smalls/SearchBar";
import ColumnHeaders from "../../../components/Smalls/ColumnHeaders";
import CaseListItem from "../../../components/Smalls/CaseListItem";
import ErrorView from "../../../components/Smalls/ErrorView";

const createStyles = (width, height) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 50,
  },
  listFooter: {
    height: 50,
  },
  activeFilterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: width * 0.05,
    marginVertical: height * 0.01,
    gap: 8,
  },
  activeFilterLabel: {
    fontSize: 14,
    color: Colors.lightText,
    fontWeight: '500',
  },
  activeFilterBadge: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  activeFilterText: {
    color: Colors.background,
    fontSize: 12,
    fontWeight: '600',
  },
  skeletonContainer: {
    flex: 1,
    paddingHorizontal: width * 0.05,
  },
  skeletonItem: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    marginBottom: 8,
  },
  skeletonIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#E0E0E0',
    marginRight: 12,
  },
  skeletonTextContainer: {
    flex: 1,
    marginRight: 12,
  },
  skeletonTitle: {
    height: 20,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    marginBottom: 8,
    width: '80%',
  },
  skeletonSubtitle: {
    height: 16,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    width: '60%',
  },
  skeletonTimeContainer: {
    width: width * 0.35,
    alignItems: 'flex-end',
  },
  skeletonTime: {
    height: 16,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    width: '40%',
    marginBottom: 8,
  },
  skeletonDate: {
    height: 16,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    width: '60%',
  },
});

export default function RecentCase() {
  const [cases, setCases] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const { width, height } = useWindowDimensions();
  const { token } = useAuth();
  const params = useLocalSearchParams();
  const [showFilters, setShowFilters] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState(RECENT_CASES_STATUS.RECEIVED);
  const styles = useMemo(() => createStyles(width, height), [width, height]);

  // Debounce search query updates
  useEffect(() => {
    const debouncedUpdate = debounce((query) => {
      setDebouncedSearchQuery(query);
    }, 300);

    debouncedUpdate(searchQuery);

    return () => {
      debouncedUpdate.cancel();
    };
  }, [searchQuery]);

  const filteredCases = useMemo(() => {
    return cases
      .filter(item => {
        if (!item || typeof item.CaseName !== 'string') return false;
        const matchesSearch = item.CaseName.toLowerCase().includes(debouncedSearchQuery.toLowerCase());
        const matchesStatus = item.status === selectedStatus;
        return matchesSearch && matchesStatus;
      })
      .sort((a, b) => {
        const dateA = new Date(a.date.split('/').reverse().join('-'));
        const dateB = new Date(b.date.split('/').reverse().join('-'));

        if (dateA.getTime() === dateB.getTime()) {
          const timeA = a.time.split(':').map(Number);
          const timeB = b.time.split(':').map(Number);

          if (timeA[0] === timeB[0]) {
            return timeB[1] - timeA[1];
          }
          return timeB[0] - timeA[0];
        }

        return dateB.getTime() - dateA.getTime();
      });
  }, [cases, debouncedSearchQuery, selectedStatus]);

  const fetchCases = useCallback(async (page = 1) => {
    try {
      setIsLoading(true);
      let allCases = [];

      validationService.validateRecentCasesStatus(selectedStatus);
      const response = await apiService.fetchRecentCases(token, selectedStatus);
      console.log(JSON.stringify(response, null, 2));
      if (response.status === 'success' && response.data?.forensicRequests) {
        allCases = response.data.forensicRequests;
      } else {
        throw new Error('Failed to fetch cases');
      }

      const formattedCases = allCases.map(item => {
        const { date, time } = timeUtils.formatCaseDateTime(item.createdAt);
        return {
          id: item._id,
          CaseName: item.caseId?.title || 'Untitled Case',
          time,
          date,
          status: item.status,
          labName: item.labId?.name || 'Unknown Lab',
          labdepartmentId: item.labdepartmentId,
          policeStation: item.policeStationId?.name || 'Unknown Police Station',
          evidenceCount: item.evidences?.length || 0
        };
      });

      if (page === 1) {
        setCases(formattedCases);
      } else {
        setCases(prevCases => [...prevCases, ...formattedCases]);
      }

      setTotalPages(1);
      setError(null);
    } catch (err) {
      console.error('Failed to fetch cases:', err);
      setError(err.message || 'Failed to load cases. Please try again.');
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  }, [token, selectedStatus]);

  useEffect(() => {
    fetchCases();
  }, [fetchCases]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    setCurrentPage(1);
    fetchCases(1);
  }, [fetchCases]);

  const loadMoreCases = useCallback(() => {
    if (currentPage < totalPages) {
      const nextPage = currentPage + 1;
      setCurrentPage(nextPage);
      fetchCases(nextPage);
    }
  }, [currentPage, totalPages, fetchCases]);

  
  const navigateToCase = useCallback((forensicRequestId) => {
    // console.log('Navigating with forensicRequestId:', forensicRequestId);
    router.push({
      pathname: "(screens)/caseDetails",
      params: { forensicRequestId: forensicRequestId },
    });
  }, []);

  const renderItem = useCallback(({ item }) => (
    <CaseListItem 
      item={item} 
      width={width} 
      onPress={() => navigateToCase(item.id)}
    />
  ), [width, navigateToCase]);

  const keyExtractor = useCallback((item) => item.id, []);

  const ListEmptyComponent = useCallback(() => (
    <View style={styles.emptyListContainer}>
      <Text>
        {searchQuery
          ? 'No matching cases found'
          : `No ${RECENT_CASES_STATUS_DISPLAY_NAMES[selectedStatus]} cases found`
        }
      </Text>
    </View>
  ), [searchQuery, selectedStatus]);

  const ListFooterComponent = useCallback(() => (
    <View style={styles.listFooter} />
  ), []);

  const getItemLayout = useCallback((data, index) => ({
    length: 100, // Approximate height of each item
    offset: 100 * index,
    index,
  }), []);

  if (isLoading) {
    return <RecentCasesSkeleton />;
  }

  if (error) {
    return <ErrorView error={error} onRetry={() => fetchCases(1)} />;
  }

  return (
    <View style={styles.container}>
      <SearchBar
        width={width}
        height={height}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        showFilters={showFilters}
        setShowFilters={setShowFilters}
      />

      <StatusFilter
        width={width}
        height={height}
        selectedStatus={selectedStatus}
        setSelectedStatus={setSelectedStatus}
        visible={showFilters}
      />

      <View style={styles.activeFilterContainer}>
        <Text style={styles.activeFilterLabel}>Active Filter:</Text>
        <View style={styles.activeFilterBadge}>
          <Text style={styles.activeFilterText}>
            {RECENT_CASES_STATUS_DISPLAY_NAMES[selectedStatus]}
          </Text>
        </View>
      </View>

      <ColumnHeaders width={width} height={height} />

      <View style={{ flex: 1 }}>
        <FlatList
          data={filteredCases}
          renderItem={renderItem}
          keyExtractor={keyExtractor}
          getItemLayout={getItemLayout}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={5}
          initialNumToRender={10}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[Colors.primary]}
              tintColor={Colors.primary}
            />
          }
          onEndReached={loadMoreCases}
          onEndReachedThreshold={0.5}
          ListEmptyComponent={ListEmptyComponent}
          ListFooterComponent={ListFooterComponent}
          updateCellsBatchingPeriod={50}
          maintainVisibleContentPosition={{
            minIndexForVisible: 0,
            autoscrollToTopThreshold: 10,
          }}
        />
      </View>
    </View>
  );
}