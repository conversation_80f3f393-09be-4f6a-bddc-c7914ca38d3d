import { View, Text, StyleSheet } from 'react-native';
import { Colors } from '../../constants/colors';

const createStyles = (width, height) => StyleSheet.create({
  columnHeaderContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginHorizontal: width * 0.05,
    marginBottom: height * 0.01,
  },
  columnHeaderText: {
    opacity: 0.5,
    color: Colors.lightText,
  },
  columnTimeContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: width * 0.4,
  },
});

const ColumnHeaders = ({ width, height }) => {
  const styles = createStyles(width, height);

  return (
    <View style={styles.columnHeaderContainer}>
      <Text style={styles.columnHeaderText}>Case Name</Text>
      <View style={styles.columnTimeContainer}>
        <Text style={styles.columnHeaderText}>Time</Text>
        <Text style={styles.columnHeaderText}>Date</Text>
      </View>
    </View>
  );
};

export default ColumnHeaders; 